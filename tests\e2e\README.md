# E2E Tests for FineWork Frontend

This directory contains end-to-end tests for the FineWork application using Playwright.

## Test Coverage

### Slack Login Flow (`slack-login.spec.ts`)

Tests the complete Slack authentication flow including:

1. **Login Page Display** - Verifies the login page loads correctly with:
   - <PERSON><PERSON> logo
   - "Sign in with <PERSON>lack" button with Slack icon
   - Copyright notice

2. **Slack OAuth Redirect** - Tests that clicking the login button:
   - Initiates redirect to Slack OAuth
   - <PERSON>perly handles the authentication flow

3. **Authentication Callback** - Verifies the callback endpoint:
   - Handles valid tokens (redirects to home)
   - Handles invalid tokens (redirects to login with error)

4. **Error Handling** - Tests error message display:
   - Shows Thai error message when login fails
   - Displays notification properly

5. **Route Protection** - Verifies middleware behavior:
   - Unauthenticated users can access login page
   - Authenticated users are redirected away from login

6. **SEO & Metadata** - Checks page configuration:
   - Proper page title
   - Favicon presence
   - Mobile viewport meta tag

## Running Tests

```bash
# Run all E2E tests
bun run test:e2e

# Run tests with UI (interactive mode)
bun run test:e2e:ui

# Run tests in headed mode (visible browser)
bun run test:e2e:headed

# Run specific browser only
bun run test:e2e --project=chromium
bun run test:e2e --project=firefox
bun run test:e2e --project=webkit

# Show HTML report
bunx playwright show-report
```

## Test Results

✅ **All tests passing** - The Slack login functionality is working correctly:

- Login page displays properly with all required elements
- Slack OAuth redirect is initiated successfully
- Authentication callback handles both success and error cases
- Error messages are displayed correctly in Thai
- Route protection middleware works as expected
- Page metadata and SEO elements are properly configured

## Test Environment

- **Target URL**: https://finework.finema.dev
- **Browsers**: Chromium, Firefox, WebKit
- **Framework**: Playwright with TypeScript
- **Assertions**: Standard Playwright expect assertions

## Notes

- Tests run against the production environment
- Mock tokens are used for callback testing
- Error notifications are tested with Thai language messages
- All tests are designed to be non-destructive and safe to run repeatedly
